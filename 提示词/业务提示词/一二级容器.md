# 一二级容器

## 一级容器

**1.主容器布局(main_container.tsx):**

- 1.全视窗高度:100%
- 2.全视窗宽度:100%
- 3.背景颜色:#242424
- 4.展示方式:弹性布局
- 5.主轴方向:水平
- 6.主轴对齐:左对齐
- 7.交叉对齐:居中
- 8.溢出处理:隐藏

## 二级容器

**1.布局结构说明:**

- 1.排列方式:二级容器在主容器内水平排列
- 2.排列结构:[二级容器布局1] [二级容器布局2或3]
- 3.容器间隔:通过[间隔]产生间隔

**2.二级容器布局1(componetA1.tsx):**

- 1.容器形状:矩形
- 2.视窗高度:全视窗高度95%
- 3.视窗宽度:全视窗高度95%
- 4.背景颜色:#6d6d6d
- 5.展示方式:弹性布局
- 6.弹性方向:垂直
- 7.对齐方式:水平，垂直居中
- 8.溢出处理:隐藏

- **3.包装容器(componet_interactionB.tsx):**

- 1.包装容器:
  - 1.包装对象:'compnetB1'和'compnetB2'
  - 2.包装作用:应用间隔和作为‘compnet4’的定位参考
- 2.间隔:
  - 1.间隔属性:‘margin-left: 1vw’
  - 2.间隔对象:‘compnetA1’
- 3.容器位置:
  - 1.容器定位:相对位置
- 4.容器尺寸:
  - 1.高度:全视窗高度的95%
  - 2.宽度:全视窗宽度的20%

**3.二级容器布局2(componetB1.tsx):**

- 1.容器形状:长方形
- 2.容器尺寸:继承包装容器的100%宽高
- 3.背景颜色:#6d6d6d
- 4.展示方式:弹性布局
- 5.弹性方向:垂直居中
- 6.溢出处理:隐藏

**4.二级容器布局3(componetB2.tsx):**

- 1.容器形状:长方形
- 2.容器尺寸:继承包装容器的100%宽高
- 3.背景颜色:#b6b6b6
- 4.展示方式:弹性布局
- 5.弹性方向:垂直居中
- 6.溢出处理:隐藏

**5.二级容器布局4(componetButton.tsx):**

- 1.容器说明:用于放置切换按键，悬浮于‘compnetB1’和‘compnetB2’区域上
- 2.视窗高度:全视窗高度的3%
- 3.视窗宽度:全视窗宽度的20%
- 4.背景颜色:无
- 5.展示方式:弹性布局
- 6.弹性方向:水平居中
- 7.溢出方式:隐藏
- 8.容器位置:绝对位置
- 9.顶部对齐:‘top: 0’
- 10.左部对齐:'left: 0'

**6.按键调用(componetButton.tsx):**

- 1.模式按键:
  - 1.调用按键'SecondaryButton'
    - 1.配置参数:
      - 1.键高:占'componetButton'容器高的100%
      - 2.键宽:占'componetButton'容器宽的50%
    - 2.文本:
      - 1.键内文本:'模式'
      - 2.文本大小:尺寸自适应
- 2.业务按键:
  - 1.调用按键‘SecondaryButton’
    - 1.配置参数:
      - 1.键高:占‘componentButton’容器高的100%
      - 2.键宽:占‘componentButton’容器宽的50%
    - 2.文本:
      - 1.键内文本:'业务'
      - 2.文本大小:尺寸自适应

**7.按键交互(componet_interactionB.tsx):**

- 1.按键状态管理:
  - 1.状态管理:所有按键状态信息存储于‘store.ts’
- 2.按键交互业务:
  - 1.点击‘模式按键’时显示'componetB1.tsx'容器，并隐藏'componetB2.tsx'容器
  - 2.点击‘业务按键’时显示'componetB2.tsx'容器，并隐藏'componetB1.tsx'容器
  - 3.默认状态:为‘模式按键’为激活状态，默认显示‘componetB1.tsx’容器
  - 4.‘compnetButton’始终保持可见，其位置不受‘compnetB1’和‘componetB2’显隐切换影响
