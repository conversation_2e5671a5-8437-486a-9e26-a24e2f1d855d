# 普通按键

## 初始提示词

- 1。阅读‘普通按键路径.md’并用于创建文件
- 2.阅读‘前端技术栈.md’并用于构建代码
- 3.严格执行‘普通按键.md’中的指示
- 4.禁止提前阅读除提示词描述以外的文档
- 5.仅完成该文本提供的逻辑流程，禁止补全
- 6.检测代码能否正常运行，创建Storybook进行可视化
- 7.更新.gitignore文档

## 优化提示词

请按照以下步骤严格执行普通按键组件开发任务：

1. **读取路径配置文档**：首先阅读 `普通按键路径.md` 文件，获取普通按键相关文件的标准路径配置信息
2. **读取技术栈配置**：阅读 `前端技术栈.md` 文件，了解项目使用的前端技术栈、依赖包和代码规范
3. **严格执行开发指示**：完全按照 `普通按键.md` 文件中的具体指示和要求进行开发，不得偏离或修改任何规定
4. **严格遵循范围限制**：
   - 仅允许阅读上述三个指定的提示词文档  
   - 禁止提前阅读项目中的其他任何文档或代码文件  
   - 不得查看现有项目结构或内容（除非文档明确要求）  
5. **精确执行开发任务**：
   - 严格按照文档要求创建文件和编写代码  
   - 仅实现文档中明确指定的功能，不得添加、修改或补充任何内容  
   - 不得根据经验或最佳实践添加额外的功能或优化  
6. **代码质量验证**：
   - 检查代码语法正确性和运行时错误  
   - 在 `apps/frontend/stories` 目录下创建对应的 Storybook 故事文件  
   - 确保组件可以在 Storybook 中正常显示和交互  
7. **更新项目配置**：
   - 检查并更新根目录的 `.gitignore` 文件  
   - 添加必要的忽略规则以排除不需要版本控制的文件  
8. **验证和报告**：
   - 列出所有创建的文件和实现的功能  
   - 报告代码验证结果和 Storybook 创建状态  
   - 如有任何问题或失败，明确报告原因和解决方案  
