# 项目文件创建

## 初始提示词

- 1.阅读‘项目文件路径.md’并用于创建文件
- 2.阅读‘容器文件路径.md’并用于创建文件
- 3.禁止提前阅读除提示词描述以外的文档
- 4.仅完成该文本提供的逻辑流程，禁止补全
- 5.创建完成后检测文件夹和文件是否创建成功

## 优化提示词

请按照以下步骤严格执行普通按键组件开发任务：

1. **读取项目结构文档**: 首先阅读 `项目文件路径.md` 文件，获取需要创建的项目文件和文件夹的完整路径结构信息
2. **读取容器配置文档**: 接着阅读 `容器文件路径.md` 文件，获取容器相关的文件和文件夹创建要求
3. **严格按照文档创建**: 根据上述两个文档中指定的路径和结构，创建所有必需的文件夹和文件
   - 使用 `save-file` 工具创建文件  
   - 确保文件夹结构完整  
   - 保持文件命名规范  
4. **验证创建结果**: 创建完成后，使用 `view` 工具检查所有指定的文件夹和文件是否已成功创建，确认文件结构与文档要求一致
5. **执行约束**:
   - 严格限制阅读范围: 除了上述两个指定的路径文档外，不得提前阅读任何其他项目文档或代码文件  
   - 严格按照流程执行: 仅执行本指南明确规定的步骤，不得自行添加额外的功能或优化  
   - 不得跳过验证步骤: 必须完成最终的文件创建验证，确保任务完整性  

6. **成功标准**:
   - 所有 `项目文件路径.md` 中指定的文件和文件夹已创建  
   - 所有 `容器文件路径.md` 中指定的文件和文件夹已创建  
   - 文件结构与文档要求完全一致  
   - 验证步骤确认创建成功  
